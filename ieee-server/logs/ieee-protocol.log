2025-09-17T13:52:08.902+08:00  INFO 16520 --- [main] c.w.ieee.IeeeServerApplication           : Starting IeeeServerApplication using Java 21.0.6 with PID 16520 (D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server\target\classes started by lujie.shen in D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server)
2025-09-17T13:52:08.904+08:00  INFO 16520 --- [main] c.w.ieee.IeeeServerApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-17T13:52:09.544+08:00  INFO 16520 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-17T13:52:09.545+08:00  INFO 16520 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-17T13:52:09.574+08:00  INFO 16520 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-09-17T13:52:10.057+08:00  INFO 16520 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-09-17T13:52:10.069+08:00  INFO 16520 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-17T13:52:10.070+08:00  INFO 16520 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-09-17T13:52:10.146+08:00  INFO 16520 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-17T13:52:10.146+08:00  INFO 16520 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1208 ms
2025-09-17T13:52:10.735+08:00  WARN 16520 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'cacheServiceImpl': Injection of resource dependencies failed
2025-09-17T13:52:10.743+08:00  INFO 16520 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-0} closing ...
2025-09-17T13:52:10.745+08:00  INFO 16520 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-09-17T13:52:10.806+08:00  INFO 16520 --- [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-17T13:52:10.816+08:00 ERROR 16520 --- [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'cacheServiceImpl': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:323) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1416) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:597) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:950) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.1.5.jar:3.1.5]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738) ~[spring-boot-3.1.5.jar:3.1.5]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440) ~[spring-boot-3.1.5.jar:3.1.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316) ~[spring-boot-3.1.5.jar:3.1.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-3.1.5.jar:3.1.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295) ~[spring-boot-3.1.5.jar:3.1.5]
	at com.weihengtech.ieee.IeeeServerApplication.main(IeeeServerApplication.java:20) ~[classes/:na]
Caused by: org.springframework.beans.factory.BeanNotOfRequiredTypeException: Bean named 'ecosIotClient' is expected to be of type 'com.weihengtech.sdk.iot.ecos.EcosIotClient' but was actually of type 'org.springframework.beans.factory.support.NullBean'
	at org.springframework.beans.factory.support.AbstractBeanFactory.adaptBeanInstance(AbstractBeanFactory.java:407) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:388) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:457) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:537) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:508) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:659) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:320) ~[spring-context-6.0.13.jar:6.0.13]
	... 17 common frames omitted

2025-09-17T13:53:15.323+08:00  INFO 10408 --- [main] c.w.ieee.IeeeServerApplication           : Starting IeeeServerApplication using Java 21.0.6 with PID 10408 (D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server\target\classes started by lujie.shen in D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server)
2025-09-17T13:53:15.325+08:00  INFO 10408 --- [main] c.w.ieee.IeeeServerApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-17T13:53:15.963+08:00  INFO 10408 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-17T13:53:15.965+08:00  INFO 10408 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-17T13:53:15.993+08:00  INFO 10408 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-09-17T13:53:16.461+08:00  INFO 10408 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-09-17T13:53:16.468+08:00  INFO 10408 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-17T13:53:16.468+08:00  INFO 10408 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-09-17T13:53:16.545+08:00  INFO 10408 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-17T13:53:16.545+08:00  INFO 10408 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1179 ms
2025-09-17T13:53:17.110+08:00  WARN 10408 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'cacheServiceImpl': Injection of resource dependencies failed
2025-09-17T13:53:17.118+08:00  INFO 10408 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-0} closing ...
2025-09-17T13:53:17.120+08:00  INFO 10408 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-09-17T13:53:17.175+08:00  INFO 10408 --- [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-17T13:53:17.186+08:00 ERROR 10408 --- [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'cacheServiceImpl': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:323) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1416) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:597) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:950) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.1.5.jar:3.1.5]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738) ~[spring-boot-3.1.5.jar:3.1.5]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440) ~[spring-boot-3.1.5.jar:3.1.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316) ~[spring-boot-3.1.5.jar:3.1.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-3.1.5.jar:3.1.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295) ~[spring-boot-3.1.5.jar:3.1.5]
	at com.weihengtech.ieee.IeeeServerApplication.main(IeeeServerApplication.java:20) ~[classes/:na]
Caused by: org.springframework.beans.factory.BeanNotOfRequiredTypeException: Bean named 'ecosIotClient' is expected to be of type 'com.weihengtech.sdk.iot.ecos.EcosIotClient' but was actually of type 'org.springframework.beans.factory.support.NullBean'
	at org.springframework.beans.factory.support.AbstractBeanFactory.adaptBeanInstance(AbstractBeanFactory.java:407) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:388) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:457) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:537) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:508) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:659) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:320) ~[spring-context-6.0.13.jar:6.0.13]
	... 17 common frames omitted

2025-09-17T13:58:20.638+08:00  INFO 29980 --- [main] c.w.ieee.IeeeServerApplication           : Starting IeeeServerApplication using Java 21.0.6 with PID 29980 (D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server\target\classes started by lujie.shen in D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server)
2025-09-17T13:58:20.640+08:00  INFO 29980 --- [main] c.w.ieee.IeeeServerApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-17T13:58:21.331+08:00  INFO 29980 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-17T13:58:21.333+08:00  INFO 29980 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-17T13:58:21.358+08:00  INFO 29980 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-09-17T13:58:21.924+08:00  INFO 29980 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-09-17T13:58:21.931+08:00  INFO 29980 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-17T13:58:21.931+08:00  INFO 29980 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-09-17T13:58:21.997+08:00  INFO 29980 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-17T13:58:21.997+08:00  INFO 29980 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1324 ms
2025-09-17T13:58:22.937+08:00  INFO 29980 --- [main] g.s.a.GrpcServerFactoryAutoConfiguration : Detected grpc-netty-shaded: Creating ShadedNettyGrpcServerFactory
2025-09-17T13:58:23.146+08:00  INFO 29980 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 14 endpoint(s) beneath base path '/actuator'
2025-09-17T13:58:23.194+08:00  INFO 29980 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-09-17T13:58:23.237+08:00  INFO 29980 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: rpc.EndDeviceService, bean: endDeviceServer, class: com.weihengtech.ieee.server.EndDeviceServer
2025-09-17T13:58:23.237+08:00  INFO 29980 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.health.v1.Health, bean: grpcHealthService, class: io.grpc.protobuf.services.HealthServiceImpl
2025-09-17T13:58:23.237+08:00  INFO 29980 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.reflection.v1alpha.ServerReflection, bean: protoReflectionService, class: io.grpc.protobuf.services.ProtoReflectionService
2025-09-17T13:58:23.239+08:00  WARN 29980 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'shadedNettyGrpcServerLifecycle'
2025-09-17T13:58:23.248+08:00  INFO 29980 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-0} closing ...
2025-09-17T13:58:23.253+08:00  INFO 29980 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-09-17T13:58:23.307+08:00  INFO 29980 --- [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-17T13:58:23.324+08:00 ERROR 29980 --- [main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

An attempt was made to call a method that does not exist. The attempt was made from the following location:

    net.devh.boot.grpc.server.metrics.MetricsServerInstruments.newServerMetricsMeters(MetricsServerInstruments.java:69)

The following method did not exist:

    'io.micrometer.core.instrument.Meter$MeterProvider io.micrometer.core.instrument.Counter$Builder.withRegistry(io.micrometer.core.instrument.MeterRegistry)'

The calling method's class, net.devh.boot.grpc.server.metrics.MetricsServerInstruments, was loaded from the following location:

    jar:file:/D:/Program%20Files/apache-maven-3.9.9/repository/net/devh/grpc-server-spring-boot-starter/3.1.0.RELEASE/grpc-server-spring-boot-starter-3.1.0.RELEASE.jar!/net/devh/boot/grpc/server/metrics/MetricsServerInstruments.class

The called method's class, io.micrometer.core.instrument.Counter$Builder, is available from the following locations:

    jar:file:/D:/Program%20Files/apache-maven-3.9.9/repository/io/micrometer/micrometer-core/1.11.5/micrometer-core-1.11.5.jar!/io/micrometer/core/instrument/Counter$Builder.class

The called method's class hierarchy was loaded from the following locations:

    io.micrometer.core.instrument.Counter.Builder: file:/D:/Program%20Files/apache-maven-3.9.9/repository/io/micrometer/micrometer-core/1.11.5/micrometer-core-1.11.5.jar


Action:

Correct the classpath of your application so that it contains compatible versions of the classes net.devh.boot.grpc.server.metrics.MetricsServerInstruments and io.micrometer.core.instrument.Counter$Builder

2025-09-17T14:00:17.249+08:00  INFO 31784 --- [main] c.w.ieee.IeeeServerApplication           : Starting IeeeServerApplication using Java 21.0.6 with PID 31784 (D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server\target\classes started by lujie.shen in D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server)
2025-09-17T14:00:17.250+08:00  INFO 31784 --- [main] c.w.ieee.IeeeServerApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-17T14:00:17.916+08:00  INFO 31784 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-17T14:00:17.918+08:00  INFO 31784 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-17T14:00:17.945+08:00  INFO 31784 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-09-17T14:00:18.523+08:00  INFO 31784 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-09-17T14:00:18.531+08:00  INFO 31784 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-17T14:00:18.531+08:00  INFO 31784 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-09-17T14:00:18.598+08:00  INFO 31784 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-17T14:00:18.598+08:00  INFO 31784 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1314 ms
2025-09-17T14:00:19.569+08:00  INFO 31784 --- [main] g.s.a.GrpcServerFactoryAutoConfiguration : Detected grpc-netty-shaded: Creating ShadedNettyGrpcServerFactory
2025-09-17T14:00:19.784+08:00  INFO 31784 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 14 endpoint(s) beneath base path '/actuator'
2025-09-17T14:00:19.832+08:00  INFO 31784 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-09-17T14:00:19.874+08:00  INFO 31784 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: rpc.EndDeviceService, bean: endDeviceServer, class: com.weihengtech.ieee.server.EndDeviceServer
2025-09-17T14:00:19.874+08:00  INFO 31784 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.health.v1.Health, bean: grpcHealthService, class: io.grpc.protobuf.services.HealthServiceImpl
2025-09-17T14:00:19.875+08:00  INFO 31784 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.reflection.v1alpha.ServerReflection, bean: protoReflectionService, class: io.grpc.protobuf.services.ProtoReflectionService
2025-09-17T14:00:19.876+08:00  WARN 31784 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'shadedNettyGrpcServerLifecycle'
2025-09-17T14:00:19.885+08:00  INFO 31784 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-0} closing ...
2025-09-17T14:00:19.890+08:00  INFO 31784 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-09-17T14:00:19.945+08:00  INFO 31784 --- [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-17T14:00:19.963+08:00 ERROR 31784 --- [main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

An attempt was made to call a method that does not exist. The attempt was made from the following location:

    net.devh.boot.grpc.server.metrics.MetricsServerInstruments.newServerMetricsMeters(MetricsServerInstruments.java:69)

The following method did not exist:

    'io.micrometer.core.instrument.Meter$MeterProvider io.micrometer.core.instrument.Counter$Builder.withRegistry(io.micrometer.core.instrument.MeterRegistry)'

The calling method's class, net.devh.boot.grpc.server.metrics.MetricsServerInstruments, was loaded from the following location:

    jar:file:/D:/Program%20Files/apache-maven-3.9.9/repository/net/devh/grpc-server-spring-boot-starter/3.1.0.RELEASE/grpc-server-spring-boot-starter-3.1.0.RELEASE.jar!/net/devh/boot/grpc/server/metrics/MetricsServerInstruments.class

The called method's class, io.micrometer.core.instrument.Counter$Builder, is available from the following locations:

    jar:file:/D:/Program%20Files/apache-maven-3.9.9/repository/io/micrometer/micrometer-core/1.11.5/micrometer-core-1.11.5.jar!/io/micrometer/core/instrument/Counter$Builder.class

The called method's class hierarchy was loaded from the following locations:

    io.micrometer.core.instrument.Counter.Builder: file:/D:/Program%20Files/apache-maven-3.9.9/repository/io/micrometer/micrometer-core/1.11.5/micrometer-core-1.11.5.jar


Action:

Correct the classpath of your application so that it contains compatible versions of the classes net.devh.boot.grpc.server.metrics.MetricsServerInstruments and io.micrometer.core.instrument.Counter$Builder

2025-09-17T14:02:43.220+08:00  INFO 25268 --- [ieee-protocol-server] [main] c.w.ieee.IeeeServerApplication           : Starting IeeeServerApplication using Java 21.0.6 with PID 25268 (D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server\target\classes started by lujie.shen in D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server)
2025-09-17T14:02:43.221+08:00  INFO 25268 --- [ieee-protocol-server] [main] c.w.ieee.IeeeServerApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-17T14:02:43.895+08:00  INFO 25268 --- [ieee-protocol-server] [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-17T14:02:43.897+08:00  INFO 25268 --- [ieee-protocol-server] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-17T14:02:43.924+08:00  INFO 25268 --- [ieee-protocol-server] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-09-17T14:02:43.981+08:00  WARN 25268 --- [ieee-protocol-server] [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: java.lang.IllegalArgumentException: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
2025-09-17T14:02:43.988+08:00  INFO 25268 --- [ieee-protocol-server] [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-17T14:02:44.002+08:00 ERROR 25268 --- [ieee-protocol-server] [main] o.s.boot.SpringApplication               : Application run failed

java.lang.IllegalArgumentException: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.getTypeForFactoryBeanFromAttributes(FactoryBeanRegistrySupport.java:86) ~[spring-beans-6.1.5.jar:6.1.5]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryBean(AbstractAutowireCapableBeanFactory.java:837) ~[spring-beans-6.1.5.jar:6.1.5]
	at org.springframework.beans.factory.support.AbstractBeanFactory.isTypeMatch(AbstractBeanFactory.java:621) ~[spring-beans-6.1.5.jar:6.1.5]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:575) ~[spring-beans-6.1.5.jar:6.1.5]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:534) ~[spring-beans-6.1.5.jar:6.1.5]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:138) ~[spring-context-6.1.5.jar:6.1.5]
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:788) ~[spring-context-6.1.5.jar:6.1.5]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:606) ~[spring-context-6.1.5.jar:6.1.5]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.2.4.jar:3.2.4]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754) ~[spring-boot-3.2.4.jar:3.2.4]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456) ~[spring-boot-3.2.4.jar:3.2.4]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334) ~[spring-boot-3.2.4.jar:3.2.4]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354) ~[spring-boot-3.2.4.jar:3.2.4]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343) ~[spring-boot-3.2.4.jar:3.2.4]
	at com.weihengtech.ieee.IeeeServerApplication.main(IeeeServerApplication.java:20) ~[classes/:na]

2025-09-17T14:04:32.346+08:00  INFO 25936 --- [main] c.w.ieee.IeeeServerApplication           : Starting IeeeServerApplication using Java 21.0.6 with PID 25936 (D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server\target\classes started by lujie.shen in D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server)
2025-09-17T14:04:32.348+08:00  INFO 25936 --- [main] c.w.ieee.IeeeServerApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-17T14:04:33.005+08:00  INFO 25936 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-17T14:04:33.007+08:00  INFO 25936 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-17T14:04:33.035+08:00  INFO 25936 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-09-17T14:04:33.610+08:00  INFO 25936 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-09-17T14:04:33.617+08:00  INFO 25936 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-17T14:04:33.617+08:00  INFO 25936 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-09-17T14:04:33.682+08:00  INFO 25936 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-17T14:04:33.682+08:00  INFO 25936 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1303 ms
2025-09-17T14:04:34.655+08:00  INFO 25936 --- [main] g.s.a.GrpcServerFactoryAutoConfiguration : Detected grpc-netty-shaded: Creating ShadedNettyGrpcServerFactory
2025-09-17T14:04:34.888+08:00  INFO 25936 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 14 endpoint(s) beneath base path '/actuator'
2025-09-17T14:04:34.936+08:00  INFO 25936 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-09-17T14:04:34.980+08:00  INFO 25936 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: rpc.EndDeviceService, bean: endDeviceServer, class: com.weihengtech.ieee.server.EndDeviceServer
2025-09-17T14:04:34.980+08:00  INFO 25936 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.health.v1.Health, bean: grpcHealthService, class: io.grpc.protobuf.services.HealthServiceImpl
2025-09-17T14:04:34.980+08:00  INFO 25936 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.reflection.v1alpha.ServerReflection, bean: protoReflectionService, class: io.grpc.protobuf.services.ProtoReflectionService
2025-09-17T14:04:34.982+08:00  WARN 25936 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'shadedNettyGrpcServerLifecycle'
2025-09-17T14:04:34.990+08:00  INFO 25936 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-0} closing ...
2025-09-17T14:04:34.995+08:00  INFO 25936 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-09-17T14:04:35.048+08:00  INFO 25936 --- [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-17T14:04:35.060+08:00 ERROR 25936 --- [main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

An attempt was made to call a method that does not exist. The attempt was made from the following location:

    net.devh.boot.grpc.server.metrics.MetricsServerInstruments.newServerMetricsMeters(MetricsServerInstruments.java:69)

The following method did not exist:

    'io.micrometer.core.instrument.Meter$MeterProvider io.micrometer.core.instrument.Counter$Builder.withRegistry(io.micrometer.core.instrument.MeterRegistry)'

The calling method's class, net.devh.boot.grpc.server.metrics.MetricsServerInstruments, was loaded from the following location:

    jar:file:/D:/Program%20Files/apache-maven-3.9.9/repository/net/devh/grpc-server-spring-boot-starter/3.0.0.RELEASE/grpc-server-spring-boot-starter-3.0.0.RELEASE.jar!/net/devh/boot/grpc/server/metrics/MetricsServerInstruments.class

The called method's class, io.micrometer.core.instrument.Counter$Builder, is available from the following locations:

    jar:file:/D:/Program%20Files/apache-maven-3.9.9/repository/io/micrometer/micrometer-core/1.11.5/micrometer-core-1.11.5.jar!/io/micrometer/core/instrument/Counter$Builder.class

The called method's class hierarchy was loaded from the following locations:

    io.micrometer.core.instrument.Counter.Builder: file:/D:/Program%20Files/apache-maven-3.9.9/repository/io/micrometer/micrometer-core/1.11.5/micrometer-core-1.11.5.jar


Action:

Correct the classpath of your application so that it contains compatible versions of the classes net.devh.boot.grpc.server.metrics.MetricsServerInstruments and io.micrometer.core.instrument.Counter$Builder

2025-09-17T14:05:49.811+08:00  INFO 18120 --- [main] c.w.ieee.IeeeServerApplication           : Starting IeeeServerApplication using Java 21.0.6 with PID 18120 (D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server\target\classes started by lujie.shen in D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server)
2025-09-17T14:05:49.812+08:00  INFO 18120 --- [main] c.w.ieee.IeeeServerApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-17T14:05:50.509+08:00  INFO 18120 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-17T14:05:50.511+08:00  INFO 18120 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-17T14:05:50.540+08:00  INFO 18120 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-09-17T14:05:51.148+08:00  INFO 18120 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-09-17T14:05:51.159+08:00  INFO 18120 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-17T14:05:51.160+08:00  INFO 18120 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-09-17T14:05:51.235+08:00  INFO 18120 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-17T14:05:51.236+08:00  INFO 18120 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1392 ms
2025-09-17T14:05:52.206+08:00  INFO 18120 --- [main] g.s.a.GrpcServerFactoryAutoConfiguration : Detected grpc-netty-shaded: Creating ShadedNettyGrpcServerFactory
2025-09-17T14:05:52.427+08:00  INFO 18120 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 14 endpoint(s) beneath base path '/actuator'
2025-09-17T14:05:52.476+08:00  INFO 18120 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-09-17T14:05:52.534+08:00  INFO 18120 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: rpc.EndDeviceService, bean: endDeviceServer, class: com.weihengtech.ieee.server.EndDeviceServer
2025-09-17T14:05:52.534+08:00  INFO 18120 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.health.v1.Health, bean: grpcHealthService, class: io.grpc.protobuf.services.HealthServiceImpl
2025-09-17T14:05:52.535+08:00  INFO 18120 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.reflection.v1alpha.ServerReflection, bean: protoReflectionService, class: io.grpc.protobuf.services.ProtoReflectionService
2025-09-17T14:05:52.536+08:00  WARN 18120 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'shadedNettyGrpcServerLifecycle'
2025-09-17T14:05:52.545+08:00  INFO 18120 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-0} closing ...
2025-09-17T14:05:52.550+08:00  INFO 18120 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-09-17T14:05:52.607+08:00  INFO 18120 --- [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-17T14:05:52.620+08:00 ERROR 18120 --- [main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

An attempt was made to call a method that does not exist. The attempt was made from the following location:

    net.devh.boot.grpc.server.metrics.MetricsServerInstruments.newServerMetricsMeters(MetricsServerInstruments.java:69)

The following method did not exist:

    'io.micrometer.core.instrument.Meter$MeterProvider io.micrometer.core.instrument.Counter$Builder.withRegistry(io.micrometer.core.instrument.MeterRegistry)'

The calling method's class, net.devh.boot.grpc.server.metrics.MetricsServerInstruments, was loaded from the following location:

    jar:file:/D:/Program%20Files/apache-maven-3.9.9/repository/net/devh/grpc-server-spring-boot-starter/3.0.0.RELEASE/grpc-server-spring-boot-starter-3.0.0.RELEASE.jar!/net/devh/boot/grpc/server/metrics/MetricsServerInstruments.class

The called method's class, io.micrometer.core.instrument.Counter$Builder, is available from the following locations:

    jar:file:/D:/Program%20Files/apache-maven-3.9.9/repository/io/micrometer/micrometer-core/1.11.5/micrometer-core-1.11.5.jar!/io/micrometer/core/instrument/Counter$Builder.class

The called method's class hierarchy was loaded from the following locations:

    io.micrometer.core.instrument.Counter.Builder: file:/D:/Program%20Files/apache-maven-3.9.9/repository/io/micrometer/micrometer-core/1.11.5/micrometer-core-1.11.5.jar


Action:

Correct the classpath of your application so that it contains compatible versions of the classes net.devh.boot.grpc.server.metrics.MetricsServerInstruments and io.micrometer.core.instrument.Counter$Builder

2025-09-17T14:07:02.154+08:00  INFO 28232 --- [main] c.w.ieee.IeeeServerApplication           : Starting IeeeServerApplication using Java 21.0.6 with PID 28232 (D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server\target\classes started by lujie.shen in D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server)
2025-09-17T14:07:02.156+08:00  INFO 28232 --- [main] c.w.ieee.IeeeServerApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-17T14:07:02.815+08:00  INFO 28232 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-17T14:07:02.817+08:00  INFO 28232 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-17T14:07:02.844+08:00  INFO 28232 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-09-17T14:07:03.425+08:00  INFO 28232 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-09-17T14:07:03.432+08:00  INFO 28232 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-17T14:07:03.432+08:00  INFO 28232 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-09-17T14:07:03.499+08:00  INFO 28232 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-17T14:07:03.500+08:00  INFO 28232 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1308 ms
2025-09-17T14:07:04.401+08:00  INFO 28232 --- [main] g.s.a.GrpcServerFactoryAutoConfiguration : Detected grpc-netty-shaded: Creating ShadedNettyGrpcServerFactory
2025-09-17T14:07:04.643+08:00  INFO 28232 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 13 endpoint(s) beneath base path '/actuator'
2025-09-17T14:07:04.686+08:00  INFO 28232 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-09-17T14:07:04.730+08:00  INFO 28232 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: rpc.EndDeviceService, bean: endDeviceServer, class: com.weihengtech.ieee.server.EndDeviceServer
2025-09-17T14:07:04.731+08:00  INFO 28232 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.health.v1.Health, bean: grpcHealthService, class: io.grpc.protobuf.services.HealthServiceImpl
2025-09-17T14:07:04.731+08:00  INFO 28232 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.reflection.v1alpha.ServerReflection, bean: protoReflectionService, class: io.grpc.protobuf.services.ProtoReflectionService
2025-09-17T14:07:04.819+08:00  INFO 28232 --- [main] n.d.b.g.s.s.GrpcServerLifecycle          : gRPC Server started, listening on address: *, port: 9090
2025-09-17T14:07:04.827+08:00  INFO 28232 --- [main] c.w.ieee.IeeeServerApplication           : Started IeeeServerApplication in 2.937 seconds (process running for 3.177)
2025-09-17T14:07:04.830+08:00  INFO 28232 --- [delay-processor-1] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread 0 start
2025-09-17T14:07:04.830+08:00  INFO 28232 --- [delay-processor-2] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread 1 start
2025-09-17T14:07:04.830+08:00  INFO 28232 --- [delay-processor-3] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread 2 start
2025-09-17T14:08:03.907+08:00  INFO 28232 --- [SpringApplicationShutdownHook] n.d.b.g.s.s.GrpcServerLifecycle          : Completed gRPC server shutdown
2025-09-17T14:08:03.965+08:00 ERROR 28232 --- [delay-processor-1] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread 0 error

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727) ~[na:na]
	at java.base/java.util.concurrent.DelayQueue.take(DelayQueue.java:242) ~[na:na]
	at com.weihengtech.ieee.delay.InitUtil.lambda$initDelayQueueTaskThread$1(InitUtil.java:35) ~[ieee-core-1.0-SNAPSHOT.jar:1.0-SNAPSHOT]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-09-17T14:08:03.965+08:00 ERROR 28232 --- [delay-processor-2] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread 1 error

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727) ~[na:na]
	at java.base/java.util.concurrent.DelayQueue.take(DelayQueue.java:242) ~[na:na]
	at com.weihengtech.ieee.delay.InitUtil.lambda$initDelayQueueTaskThread$1(InitUtil.java:35) ~[ieee-core-1.0-SNAPSHOT.jar:1.0-SNAPSHOT]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-09-17T14:08:03.965+08:00 ERROR 28232 --- [delay-processor-3] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread 2 error

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727) ~[na:na]
	at java.base/java.util.concurrent.DelayQueue.take(DelayQueue.java:242) ~[na:na]
	at com.weihengtech.ieee.delay.InitUtil.lambda$initDelayQueueTaskThread$1(InitUtil.java:35) ~[ieee-core-1.0-SNAPSHOT.jar:1.0-SNAPSHOT]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

