spring.application.name=ieee-protocol-server
# 开启grpc，web服务保留最小资源
spring.main.web-application-type=servlet
grpc.server.port=9090

# 最小化Tomcat活跃线程
server.tomcat.threads.min-spare=1

# druid
# spring.autoconfigure.exclude=com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.initial-size=10
spring.datasource.druid.min-idle=10
spring.datasource.druid.max-active=100
spring.datasource.druid.max-wait=3000
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=30
spring.datasource.druid.time-between-eviction-runs-millis=30000
spring.datasource.druid.min-evictable-idle-time-millis=30000
spring.datasource.druid.test-on-borrow=true
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-return=true
spring.datasource.druid.remove-abandoned=true
spring.datasource.druid.remove-abandoned-timeout=120
spring.datasource.druid.connection-error-retry-attempts=3

# 数据库连接
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=${MYSQL_URL:****************************************************************************************************************************}
spring.datasource.username=${MYSQL_USERNAME:root}
spring.datasource.password=${MYSQL_PASSWORD:test1234}

# mybatis
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml

# log
logging.level.com.com.weihengtech=debug
logging.level.org.springframework.core=INFO
logging.level.com.xxl=ERROR
logging.file.name=./logs/ieee-protocol.log

# ecos iot - 临时配置，避免启动错误
sdk.iot.ecos.base-url=${SDK_IOT_ECOS_BASE_URL:http://ecos-iot.dev.weiheng-tech.com}
sdk.iot.ecos.read-timeout=${SDK_IOT_ECOS_READ_TIMEOUT:20}
sdk.iot.ecos.flag.elink=${SDK_IOT_ECOS_FLAG_ELINK:0}
sdk.iot.ecos.flag.tuya=${SDK_IOT_ECOS_FLAG_TUYA:1}
sdk.iot.ecos.flag.ocpp=${SDK_IOT_ECOS_FLAG_OCPP:2}
sdk.iot.ecos.flag.wh=${SDK_IOT_ECOS_FLAG_WH:3}

# redis
spring.data.redis.host=${REDIS_HOST:***************}
spring.data.redis.port=${REDIS_PORT:31270}
spring.data.redis.database=${REDIS_DB:1}
spring.data.redis.password=${REDIS_PASSWORD:}
spring.data.redis.timeout=2000
spring.data.redis.jedis.pool.max-wait=2000
spring.data.redis.jedis.pool.time-between-eviction-runs=20000

task.call.period=${TASK_CALL_PERIOD:10}
task.call.extra=${TASK_CALL_EXTRA:25}

# actuator
management.endpoints.web.exposure.include=*
management.metrics.enable.executor=true