package com.weihengtech.ieee.interceptor;

import io.grpc.ForwardingServerCall;
import io.grpc.ForwardingServerCallListener;
import io.grpc.Metadata;
import io.grpc.ServerCall;
import io.grpc.ServerCallHandler;
import io.grpc.ServerInterceptor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/25 11:38
 */
@Slf4j
public class LogInterceptor implements ServerInterceptor {

    /** 耗时线程本地缓存 */
    private static final ThreadLocal<Long> STOPWATCH_HOLDER = new ThreadLocal<>();

    @Override
    public <ReqT, RespT> ServerCall.Listener<ReqT> interceptCall(ServerCall<ReqT, RespT> call, Metadata headers, ServerCallHandler<ReqT, RespT> next) {
        CustomServerCall<ReqT, RespT> customServerCall = new CustomServerCall<>(call, STOPWATCH_HOLDER);
        ServerCall.Listener<ReqT> listener = next.startCall(customServerCall, headers);
        return new CustomServerCallListener<>(listener, STOPWATCH_HOLDER);
    }
}

@Slf4j
class CustomServerCallListener<ReqT> extends ForwardingServerCallListener.SimpleForwardingServerCallListener<ReqT> {

    private final ThreadLocal<Long> stopWatchHolder;

    protected CustomServerCallListener(ServerCall.Listener<ReqT> delegate, ThreadLocal<Long> stopWatchHolder) {
        super(delegate);
        this.stopWatchHolder = stopWatchHolder;
    }

    @Override
    public void onMessage(ReqT message) {
        this.stopWatchHolder.set(System.currentTimeMillis());
        String paramStr = message.toString();
        log.info("rpc request param is: {}", paramStr.length() > 2000 ? paramStr.substring(0, 2000) : paramStr);
        super.onMessage(message);
    }
}


@Slf4j
class CustomServerCall<ReqT, RespT> extends ForwardingServerCall.SimpleForwardingServerCall<ReqT, RespT> {

    private final ThreadLocal<Long> stopWatchHolder;

    protected CustomServerCall(ServerCall<ReqT, RespT> delegate, ThreadLocal<Long> stopWatchHolder) {
        super(delegate);
        this.stopWatchHolder = stopWatchHolder;
    }

    @Override
    public void sendMessage(RespT message) {
        long elapsed = 0;
        Long startTime = this.stopWatchHolder.get();
        if (startTime != null) {
            elapsed = System.currentTimeMillis() - startTime;
        }
        String resStr = message.toString();
        log.info("elapsed : {}, rpc response is: {}", elapsed, resStr.length() > 2000 ? resStr.substring(0, 2000) : resStr);
        this.stopWatchHolder.remove();
        super.sendMessage(message);
    }
}
